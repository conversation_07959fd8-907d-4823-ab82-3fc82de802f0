; AutoHotkey v2 脚本 - 一键打开网站
; 描述：使用热键快速打开常用网站

#Requires AutoHotkey v2.0        ; 要求AutoHotkey v2版本
#SingleInstance Force            ; 强制单实例运行，避免重复启动

; ===========================================
; 热键定义区域
; ===========================================

; F1键 - 打开百度
F1:: {
    Run("https://www.baidu.com")
    ; 可选：显示提示信息
    ToolTip("正在打开百度...", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)  ; 2秒后隐藏提示
}

; F2键 - 打开GitHub
F2:: {
    Run("https://github.com")
    ToolTip("正在打开GitHub...", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F3键 - 打开淘宝
F3:: {
    Run("https://www.taobao.com")
    ToolTip("正在打开淘宝...", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; Ctrl+Alt+G - 打开Google（组合键示例）
^!g:: {
    Run("https://www.google.com")
    ToolTip("正在打开Google...", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; ===========================================
; 高级功能示例
; ===========================================

; F4键 - 智能打开网站（检查浏览器）
F4:: {
    网站地址 := "https://www.bilibili.com"
    
    ; 尝试在Chrome中打开
    try {
        Run('chrome.exe "' . 网站地址 . '"')
        ToolTip("在Chrome中打开B站...", , , 1)
    } catch {
        ; 如果Chrome不存在，使用默认浏览器
        Run(网站地址)
        ToolTip("在默认浏览器中打开B站...", , , 1)
    }
    
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F5键 - 打开多个网站
F5:: {
    网站列表 := [
        "https://www.zhihu.com",
        "https://www.douban.com",
        "https://www.weibo.com"
    ]
    
    ; 循环打开所有网站
    for 网站 in 网站列表 {
        Run(网站)
        Sleep(500)  ; 每个网站间隔0.5秒，避免同时打开太多
    }
    
    ToolTip("正在打开多个网站...", , , 1)
    SetTimer(() => ToolTip("", , , 1), -3000)
}

; ===========================================
; 实用工具热键
; ===========================================

; ESC键 - 退出脚本（开发调试用）
Esc::ExitApp()

; F12键 - 显示帮助信息
F12:: {
    帮助信息 := "
    (
    网站快捷键帮助：
    
    F1  - 打开百度
    F2  - 打开GitHub  
    F3  - 打开淘宝
    F4  - 打开B站（智能选择浏览器）
    F5  - 打开多个网站
    
    Ctrl+Alt+G - 打开Google
    
    F12 - 显示此帮助
    ESC - 退出脚本
    )"
    
    MsgBox(帮助信息, "网站快捷键帮助", "OK")
}
