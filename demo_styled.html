﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>带样式的网页</title>
    <style>
        /* CSS样式让网页更美观 */
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #34495e;
            margin-top: 30px;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }

        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }

        .button:hover {
            background-color: #2980b9;
        }

        ul {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
        }

        li {
            margin: 10px 0;
            padding: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>美观的网页设计</h1>

        <div class="highlight">
            <strong>提示：</strong>这个网页使用了CSS样式，看起来更加美观和专业！
        </div>

        <h2>CSS的作用：</h2>
        <ul>
            <li>控制网页的颜色和字体</li>
            <li>设置布局和间距</li>
            <li>添加阴影和圆角效果</li>
            <li>创建悬停动画效果</li>
        </ul>

        <h2>试试这些按钮：</h2>
        <a href="#" class="button">主要按钮</a>
        <a href="#" class="button" style="background-color: #e74c3c;">红色按钮</a>
        <a href="#" class="button" style="background-color: #27ae60;">绿色按钮</a>

        <h2>关于AutoHotkey：</h2>
        <p>AutoHotkey是一个强大的Windows自动化工具，不仅可以创建热键和自动化脚本，还可以像这样生成网页文件！</p>

        <p style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <em>这个网页完全由AutoHotkey脚本生成</em>
        </p>
    </div>
</body>
</html>