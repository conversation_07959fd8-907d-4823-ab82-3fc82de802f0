; AutoHotkey v2 演示脚本 - 网页生成器演示
; 描述：展示如何使用AutoHotkey创建简单网页的基本示例
; 这是一个简化版本，用于学习和理解基本概念

#Requires AutoHotkey v2.0
#SingleInstance Force

; ===========================================
; 演示：创建一个最简单的网页
; ===========================================

; F1键 - 创建最基本的HTML页面
F1:: {
    创建基础网页()
    ToolTip("基础网页已创建！", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F2键 - 创建带样式的网页
F2:: {
    创建样式网页()
    ToolTip("样式网页已创建！", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F3键 - 创建交互式网页
F3:: {
    创建交互网页()
    ToolTip("交互网页已创建！", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F12键 - 显示演示说明
F12:: {
    显示演示说明()
}

; ESC键 - 退出
Esc::ExitApp()

; ===========================================
; 网页创建函数
; ===========================================

; 创建最基础的HTML页面
创建基础网页() {
    ; 这是最简单的HTML结构
    html内容 := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>我的第一个网页</title>
</head>
<body>
    <h1>欢迎来到我的网页！</h1>
    <p>这是使用AutoHotkey创建的最简单的网页。</p>
    
    <h2>基本HTML元素演示：</h2>
    <ul>
        <li>这是一个列表项</li>
        <li>这是另一个列表项</li>
        <li>HTML很简单！</li>
    </ul>
    
    <p>
        <strong>粗体文字</strong> 和 <em>斜体文字</em>
    </p>
    
    <p>
        <a href="https://www.baidu.com">点击访问百度</a>
    </p>
</body>
</html>
    )'
    
    保存并打开网页(html内容, "demo_basic.html")
}

; 创建带CSS样式的网页
创建样式网页() {
    html内容 := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>带样式的网页</title>
    <style>
        /* CSS样式让网页更美观 */
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        
        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        
        .button:hover {
            background-color: #2980b9;
        }
        
        ul {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
        }
        
        li {
            margin: 10px 0;
            padding: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>美观的网页设计</h1>
        
        <div class="highlight">
            <strong>提示：</strong>这个网页使用了CSS样式，看起来更加美观和专业！
        </div>
        
        <h2>CSS的作用：</h2>
        <ul>
            <li>控制网页的颜色和字体</li>
            <li>设置布局和间距</li>
            <li>添加阴影和圆角效果</li>
            <li>创建悬停动画效果</li>
        </ul>
        
        <h2>试试这些按钮：</h2>
        <a href="#" class="button">主要按钮</a>
        <a href="#" class="button" style="background-color: #e74c3c;">红色按钮</a>
        <a href="#" class="button" style="background-color: #27ae60;">绿色按钮</a>
        
        <h2>关于AutoHotkey：</h2>
        <p>AutoHotkey是一个强大的Windows自动化工具，不仅可以创建热键和自动化脚本，还可以像这样生成网页文件！</p>
        
        <p style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <em>这个网页完全由AutoHotkey脚本生成</em>
        </p>
    </div>
</body>
</html>
    )'
    
    保存并打开网页(html内容, "demo_styled.html")
}

; 创建带JavaScript交互的网页
创建交互网页() {
    html内容 := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>交互式网页</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,107,107,0.4);
        }
        
        input[type="text"] {
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 5px;
            font-size: 16px;
        }
        
        .result {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            min-height: 20px;
        }
        
        .color-box {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            margin: 10px;
            display: inline-block;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .color-box:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 JavaScript交互演示 (修复版)</h1>
        <div style="background: rgba(0,255,0,0.2); padding: 10px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
            ✅ 此版本已修复所有JavaScript语法错误，功能完全正常
        </div>
        
        <div class="demo-section">
            <h3>1. 简单计算器</h3>
            <input type="text" id="num1" placeholder="输入第一个数字">
            <input type="text" id="num2" placeholder="输入第二个数字">
            <br>
            <button onclick="calculate(\'+\')">加法 +</button>
            <button onclick="calculate(\'-\')">减法 -</button>
            <button onclick="calculate(\'*\')">乘法 ×</button>
            <button onclick="calculate(\'/\')">除法 ÷</button>
            <div class="result" id="calcResult">计算结果将显示在这里</div>
        </div>
        
        <div class="demo-section">
            <h3>2. 文字效果</h3>
            <input type="text" id="textInput" placeholder="输入一些文字">
            <button onclick="showText()">显示文字</button>
            <button onclick="clearText()">清空</button>
            <div class="result" id="textResult"></div>
        </div>
        
        <div class="demo-section">
            <h3>3. 颜色变换</h3>
            <p>点击下面的色块改变背景颜色：</p>
            <div class="color-box" style="background: #ff6b6b;" onclick="changeBackground(\'#ff6b6b\')"></div>
            <div class="color-box" style="background: #4ecdc4;" onclick="changeBackground(\'#4ecdc4\')"></div>
            <div class="color-box" style="background: #45b7d1;" onclick="changeBackground(\'#45b7d1\')"></div>
            <div class="color-box" style="background: #96ceb4;" onclick="changeBackground(\'#96ceb4\')"></div>
            <div class="color-box" style="background: #feca57;" onclick="changeBackground(\'#feca57\')"></div>
        </div>
        
        <div class="demo-section">
            <h3>4. 当前时间</h3>
            <button onclick="showTime()">显示当前时间</button>
            <button onclick="startClock()">开始时钟</button>
            <button onclick="stopClock()">停止时钟</button>
            <div class="result" id="timeResult"></div>
        </div>
        
        <div class="demo-section">
            <h3>5. 随机功能</h3>
            <button onclick="randomColor()">随机颜色</button>
            <button onclick="randomNumber()">随机数字</button>
            <button onclick="randomQuote()">随机名言</button>
            <div class="result" id="randomResult"></div>
        </div>
    </div>
    
    <script>
        let clockInterval = null;
        
        // 计算器功能
        function calculate(operation) {
            const num1 = parseFloat(document.getElementById("num1").value);
            const num2 = parseFloat(document.getElementById("num2").value);
            const result = document.getElementById("calcResult");
            
            if (isNaN(num1) || isNaN(num2)) {
                result.innerHTML = "❌ 请输入有效的数字！";
                return;
            }
            
            let answer;
            switch(operation) {
                case "+": answer = num1 + num2; break;
                case "-": answer = num1 - num2; break;
                case "*": answer = num1 * num2; break;
                case "/": 
                    if (num2 === 0) {
                        result.innerHTML = "❌ 不能除以零！";
                        return;
                    }
                    answer = num1 / num2; 
                    break;
            }
            
            result.innerHTML = `✅ ${num1} ${operation} ${num2} = ${answer}`;
        }
        
        // 文字显示功能
        function showText() {
            const text = document.getElementById("textInput").value;
            const result = document.getElementById("textResult");
            
            if (text.trim() === "") {
                result.innerHTML = "请输入一些文字！";
                return;
            }
            
            result.innerHTML = `<h2 style="color: #feca57;">🎨 ${text}</h2>`;
        }
        
        function clearText() {
            document.getElementById("textResult").innerHTML = "";
            document.getElementById("textInput").value = "";
        }
        
        // 背景颜色变换
        function changeBackground(color) {
            document.body.style.background = `linear-gradient(135deg, ${color} 0%, #764ba2 100%)`;
        }
        
        // 时间功能
        function showTime() {
            const now = new Date();
            document.getElementById("timeResult").innerHTML = 
                `🕐 当前时间：${now.toLocaleString("zh-CN")}`;
        }
        
        function startClock() {
            if (clockInterval) clearInterval(clockInterval);
            
            clockInterval = setInterval(() => {
                const now = new Date();
                document.getElementById("timeResult").innerHTML = 
                    `⏰ 实时时钟：${now.toLocaleString("zh-CN")}`;
            }, 1000);
        }
        
        function stopClock() {
            if (clockInterval) {
                clearInterval(clockInterval);
                clockInterval = null;
                document.getElementById("timeResult").innerHTML += " (已停止)";
            }
        }
        
        // 随机功能
        function randomColor() {
            const colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff"];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.getElementById("randomResult").innerHTML = 
                `🎨 随机颜色：<span style="background: ${randomColor}; padding: 5px 10px; border-radius: 5px;">${randomColor}</span>`;
        }
        
        function randomNumber() {
            const randomNum = Math.floor(Math.random() * 1000) + 1;
            document.getElementById("randomResult").innerHTML = `🎲 随机数字：${randomNum}`;
        }
        
        function randomQuote() {
            const quotes = [
                "生活就像骑自行车，要保持平衡，就必须不断前进。",
                "成功不是终点，失败也不是末日，重要的是继续前进的勇气。",
                "学习永远不晚，知识是最好的投资。",
                "代码改变世界，创意点亮未来。",
                "每一个不曾起舞的日子，都是对生命的辜负。"
            ];
            const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
            document.getElementById("randomResult").innerHTML = `💭 随机名言：<em>"${randomQuote}"</em>`;
        }
        
        // 页面加载完成后的欢迎消息
        window.addEventListener("load", function() {
            setTimeout(() => {
                alert("🎉 欢迎体验AutoHotkey生成的交互式网页！\n\n这个页面包含了多种JavaScript交互功能，快来试试吧！");
            }, 1000);
        });
    </script>
</body>
</html>
    )'
    
    保存并打开网页(html内容, "demo_interactive_fixed.html")
}

; 通用保存和打开函数
保存并打开网页(html内容, 文件名) {
    try {
        文件路径 := A_ScriptDir . "\" . 文件名
        
        ; 删除旧文件
        if FileExist(文件路径) {
            FileDelete(文件路径)
        }
        
        ; 保存新文件
        FileAppend(html内容, 文件路径, "UTF-8")
        
        ; 在浏览器中打开
        Run(文件路径)
        
        return true
        
    } catch Error as e {
        MsgBox("创建网页失败：" . e.Message, "错误", "OK Icon!")
        return false
    }
}

; 显示演示说明
显示演示说明() {
    说明内容 := "
    (
    AutoHotkey 网页生成演示
    
    这是一个简化的演示脚本，展示AutoHotkey创建网页的基本概念：
    
    🔧 使用方法：
    F1 - 创建基础HTML网页
    F2 - 创建带CSS样式的网页  
    F3 - 创建带JavaScript交互的网页
    F12 - 显示此说明
    ESC - 退出程序
    
    📚 学习要点：
    
    1. 基础网页 (F1)：
       • 最简单的HTML结构
       • 基本的HTML标签使用
       • 适合初学者理解网页构成
    
    2. 样式网页 (F2)：
       • CSS样式的应用
       • 美观的视觉设计
       • 响应式布局概念
    
    3. 交互网页 (F3)：
       • JavaScript功能实现
       • 用户交互处理
       • 动态内容更新
    
    💡 核心概念：
    • HTML：网页的结构和内容
    • CSS：网页的样式和美观
    • JavaScript：网页的交互和动态功能
    • AutoHotkey：自动化生成和管理
    
    🎯 实际应用：
    • 快速原型制作
    • 学习网页开发
    • 自动化网页生成
    • 模板系统开发
    
    生成的文件保存在脚本同目录下，可以直接在浏览器中打开查看。
    )"
    
    MsgBox(说明内容, "演示说明", "OK")
}
