; 测试F3功能 - 打开fixed_interactive.html
#Requires AutoHotkey v2.0
#SingleInstance Force

; 包含demo.ahk中的函数
#Include demo.ahk

; 测试打开修复版交互网页
MsgBox("正在测试F3功能...", "测试开始", "OK")

; 调用函数
result := 打开修复版交互网页()

if (result) {
    MsgBox("✅ 测试成功！`n`nF3功能已修改为直接打开 fixed_interactive.html`n`n这个文件包含：`n• 修复的JavaScript代码`n• 一键测试功能`n• 键盘快捷键`n• 预填充数据", "测试成功", "OK")
} else {
    MsgBox("❌ 测试失败！`n`n请检查 fixed_interactive.html 文件是否存在。", "测试失败", "OK Icon!")
}

ExitApp()
